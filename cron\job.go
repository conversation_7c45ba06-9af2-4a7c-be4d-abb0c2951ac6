package cron

import (
	"os/exec"

	"github.com/astaxie/beego"
	"github.com/beevik/etree"
	"github.com/george518/PPGo_ApiAdmin/models"
)

// Test1 123
func Test1() {
	beego.Info("Run by func Test1!!!!!")

}

// Test2 123
func Test2() {
	beego.Info("Run by func Test2!!!!!")
}

// Job job
type Job struct {
}

// Run run
func (j Job) Run() {
	beego.Info("开始同步第三方设备配置。。。")

	if models.OtherDeviceGetByUploadStatus() {
		//  存在未同步数据
		doc := etree.NewDocument()
		doc.CreateProcInst("xml", `version="1.0" encoding="GBK"`)

		whiteList := doc.CreateElement("white_list")
		result, count := models.OtherDeviceGetByStatus()
		for _, v := range result {
			item := whiteList.CreateElement("item")
			deviceSerial := item.CreateElement("device_serial")
			projectName := item.CreateElement("project_name")
			deviceSerial.CreateText(v.DeviceSerial)
			projectName.CreateText(v.ProjectName)
		}

		beego.Info(count)
		doc.Indent(2)
		filename := beego.AppConfig.String("otherwhitelist")
		// doc.WriteTo(os.Stdout)
		doc.WriteToFile(filename)

		err := models.UpdateUploadStatus()
		if err != nil {
			beego.Error("更新数据异常！", err)
		}

		Command(beego.AppConfig.String("restartserver"))

	} else {
		beego.Info("无需同步数据。")
	}
}

// Command 这里为了简化，我省去了stderr和其他信息
func Command(cmd string) error {
	c := exec.Command("bash", "-c", cmd)
	// 此处是windows版本
	// c := exec.Command("cmd", "/C", cmd)
	output, err := c.CombinedOutput()
	beego.Info("重启服务结果：", string(output))
	return err
}
