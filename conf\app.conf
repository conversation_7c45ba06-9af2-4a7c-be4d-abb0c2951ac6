appname = PPGo_ApiAdmin
httpport = 8081
runmode = dev

# 站点名称
site.name = 滁州扬尘设备管理平台


# 数据库选择配置，增加sqlite3支持，值为 mysql 或 sqlite3
choicedb = sqlite3

# mysql数据库配置（上面choicedb配置mysql时生效）
# 数据库配置
db.host = ************
db.user = root
db.password = "123456"
db.port = 3306
db.name = ppgo_api_admin
db.prefix = pp_
db.timezone = Asia/Shanghai

# sqlite3数据库配置（上面choicedb配置sqlite3时生效）
db.sqlite3=./deviceServer.db

spec = 0/30 * * * * *
otherwhitelist ="other_white_list.properties"
restartserver = "/home/<USER>/Projects/deviceServer/startup.sh stop"
