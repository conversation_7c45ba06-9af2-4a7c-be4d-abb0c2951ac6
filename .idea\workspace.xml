<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="53b6b3fd-8c91-44da-b082-fbecc47d9a3f" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/conf/app.conf" beforeDir="false" afterPath="$PROJECT_DIR$/conf/app.conf" afterDir="false" />
    </list>
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FUSProjectUsageTrigger">
    <session id="1633030355">
      <usages-collector id="statistics.lifecycle.project">
        <counts>
          <entry key="project.closed" value="1" />
          <entry key="project.open.time.10" value="1" />
          <entry key="project.open.time.2" value="1" />
          <entry key="project.opened" value="2" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.open">
        <counts>
          <entry key="conf" value="3" />
          <entry key="go" value="12" />
          <entry key="md" value="4" />
          <entry key="sh" value="1" />
          <entry key="sql" value="2" />
          <entry key="yml" value="2" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.open">
        <counts>
          <entry key="Bash" value="1" />
          <entry key="Go" value="12" />
          <entry key="Markdown" value="4" />
          <entry key="PLAIN_TEXT" value="3" />
          <entry key="SQL" value="2" />
          <entry key="YAML" value="2" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.edit">
        <counts>
          <entry key="conf" value="5" />
          <entry key="md" value="181" />
          <entry key="yml" value="101" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.edit">
        <counts>
          <entry key="Markdown" value="181" />
          <entry key="PLAIN_TEXT" value="5" />
          <entry key="YAML" value="101" />
        </counts>
      </usages-collector>
    </session>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/main.go">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="240">
              <caret line="16" column="15" selection-start-line="16" selection-start-column="15" selection-end-line="16" selection-end-column="15" />
              <folding>
                <element signature="e#14#238#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/conf/app.conf">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="15">
              <caret line="1" column="15" selection-start-line="1" selection-start-column="15" selection-end-line="1" selection-end-column="15" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor />
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/run.sh">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="57">
              <caret line="15" column="23" selection-start-line="15" selection-start-column="23" selection-end-line="15" selection-end-column="23" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="GOROOT" path="/usr/local/Cellar/go/1.11.2/libexec" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/db_tools/docker-compose.yml" />
        <option value="$PROJECT_DIR$/db_tools/dev/docker-compose.yml" />
        <option value="$PROJECT_DIR$/db_tools/readme.md" />
        <option value="$PROJECT_DIR$/conf/app.conf" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodePackageJsonFileManager">
    <packageJsonPaths>
      <path value="$PROJECT_DIR$/static/editor.md/lib/codemirror/package.json" />
      <path value="$PROJECT_DIR$/static/editor.md/package.json" />
      <path value="$PROJECT_DIR$/static/zTree3/package.json" />
    </packageJsonPaths>
  </component>
  <component name="ProjectFrameBounds" fullScreen="true">
    <option name="width" value="1280" />
    <option name="height" value="800" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="PPGo_ApiAdmin" type="b2602c69:ProjectViewProjectNode" />
              <item name="PPGo_ApiAdmin" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="PPGo_ApiAdmin" type="b2602c69:ProjectViewProjectNode" />
              <item name="PPGo_ApiAdmin" type="462c0819:PsiDirectoryNode" />
              <item name="conf" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="go.gopath.indexing.explicitly.defined" value="true" />
    <property name="go.sdk.automatically.set" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="go.autoimport" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/db_tools/image" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/db_tools/dev" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="0" width="1280" height="800" extended-state="0" />
    <layout>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.16639742" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" weight="0.32876712" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" order="8" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="9" />
      <window_info active="true" anchor="bottom" id="Terminal" order="10" visible="true" weight="0.36301368" />
      <window_info anchor="bottom" id="Event Log" order="11" side_tool="true" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Database" order="3" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/static/user.json">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <caret column="14" selection-start-column="14" selection-end-column="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/models/admin.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-306">
          <caret line="19" column="21" lean-forward="true" selection-start-line="19" selection-start-column="21" selection-end-line="19" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/controllers/apidoc.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1095">
          <caret line="79" column="22" selection-start-line="79" selection-start-column="22" selection-end-line="79" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/routers/router.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="285">
          <caret line="22" column="46" lean-forward="true" selection-start-line="22" selection-start-column="46" selection-end-line="22" selection-end-column="46" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/controllers/common.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-2256">
          <caret line="182" column="17" lean-forward="true" selection-start-line="182" selection-start-column="17" selection-end-line="182" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/controllers/login.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="345">
          <caret line="34" column="41" selection-start-line="34" selection-start-column="41" selection-end-line="34" selection-end-column="41" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/libs/string.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="210">
          <caret line="20" column="29" lean-forward="true" selection-start-line="20" selection-start-column="29" selection-end-line="20" selection-end-column="29" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/../github.com/astaxie/beego/flash.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1305">
          <caret line="91" column="5" selection-start-line="91" selection-start-column="5" selection-end-line="91" selection-end-column="5" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/../github.com/astaxie/beego/config.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1680">
          <caret line="125" column="35" selection-start-line="125" selection-start-column="35" selection-end-line="125" selection-end-column="35" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/../github.com/george518/PPGo_ApiAdmin/models/group.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="270">
          <caret line="20" column="20" selection-start-line="20" selection-start-column="20" selection-end-line="20" selection-end-column="20" />
          <folding>
            <element signature="e#268#310#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/controllers/code.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-130">
          <caret line="46" column="37" selection-start-line="46" selection-start-column="37" selection-end-line="46" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/db_tools/dev/redis.conf">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/ppgo_api_admin.sql">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="180">
          <caret line="12" column="2" lean-forward="true" selection-start-line="12" selection-start-column="2" selection-end-line="12" selection-end-column="2" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/db_tools/dev/docker-compose.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="330">
          <caret line="22" column="24" lean-forward="true" selection-start-line="22" selection-start-column="24" selection-end-line="22" selection-end-column="24" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/db_tools/readme.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="105">
            <caret line="7" column="21" lean-forward="true" selection-start-line="7" selection-start-column="21" selection-end-line="7" selection-end-column="21" />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor />
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/run.sh">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="57">
          <caret line="15" column="23" selection-start-line="15" selection-start-column="23" selection-end-line="15" selection-end-column="23" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/main.go">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="240">
          <caret line="16" column="15" selection-start-line="16" selection-start-column="15" selection-end-line="16" selection-end-column="15" />
          <folding>
            <element signature="e#14#238#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/conf/app.conf">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="15">
          <caret line="1" column="15" selection-start-line="1" selection-start-column="15" selection-end-line="1" selection-end-column="15" />
        </state>
      </provider>
    </entry>
  </component>
</project>