/**********************************************
** @Des: This file ...
** @Author: haodaquan
** @Date:   2017-09-08 00:18:02
** @Last Modified by:   haodaquan
** @Last Modified time: 2017-09-16 17:26:48
***********************************************/

package models

import (
	"net/url"
	"time"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	_ "github.com/go-sql-driver/mysql"
	_ "modernc.org/sqlite"
)

func Init() {
	choicedb := beego.AppConfig.String("choicedb")
	switch choicedb {
	case "mysql":
		dbhost := beego.AppConfig.String("db.host")
		dbport := beego.AppConfig.String("db.port")
		dbuser := beego.AppConfig.String("db.user")
		dbpassword := beego.AppConfig.String("db.password")
		dbname := beego.AppConfig.String("db.name")
		timezone := beego.AppConfig.String("db.timezone")
		if dbport == "" {
			dbport = "3306"
		}
		dsn := dbuser + ":" + dbpassword + "@tcp(" + dbhost + ":" + dbport + ")/" + dbname + "?charset=utf8"
		// fmt.Println(dsn)

		if timezone != "" {
			dsn = dsn + "&loc=" + url.QueryEscape(timezone)
		}
		orm.RegisterDataBase("default", "mysql", dsn)
		orm.RegisterModel(new(Auth), new(Role), new(RoleAuth), new(Admin),
			new(Group), new(Env), new(Code), new(ApiSource), new(ApiDetail), new(ApiPublic), new(Template), new(OtherDevice))
	case "sqlite3":
		dbSqlite3 := beego.AppConfig.String("db.sqlite3")
		orm.RegisterDriver("sqlite", orm.DRSqlite)
		orm.RegisterDataBase("default", "sqlite3", dbSqlite3)
		orm.RegisterModel(new(Auth), new(Role), new(RoleAuth), new(Admin),
			new(Group), new(Env), new(Code), new(ApiSource), new(ApiDetail), new(ApiPublic), new(Template), new(OtherDevice))
		beego.Info("加载sqlite3库:", dbSqlite3)
	default:
		beego.Info("目前不支持此数据库 ", choicedb)
		beego.Info("3秒后程序将退出。。。")
		defer panic("")
		time.Sleep(3 * time.Second)

	}

	if beego.AppConfig.String("runmode") == "dev" {
		orm.Debug = true
	}
}

func TableName(name string) string {
	return beego.AppConfig.String("db.prefix") + name
}
